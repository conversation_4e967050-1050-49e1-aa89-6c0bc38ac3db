/**
 * 动画编辑器
 */
import React from 'react';
import { Card, Form, InputNumber, Typography } from 'antd';

const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { Text } = Typography;

/**
 * 动画编辑器属性
 */
interface AnimationEditorProps {
  /** 组件数据 */
  data?: any;
  /** 数据更新回调 */
  onChange?: (data: any) => void;
}

/**
 * 动画编辑器组件
 */
const AnimationEditor: React.FC<AnimationEditorProps> = ({ data, onChange }) => {
  const [form] = Form.useForm();

  // 默认数据
  const defaultData = {
    enabled: true,
    autoPlay: false,
    loop: true,
    speed: 1.0,
    ...data
  };

  // 处理数据变化
  const handleChange = (field: string, value: any) => {
    const newData = { ...defaultData, [field]: value };
    if (onChange) {
      onChange(newData);
    }
  };

  return (
    <Card title="动画" size="small">
      <Form
        form={form}
        layout="vertical"
        initialValues={defaultData}
        onValuesChange={(changedValues) => {
          Object.entries(changedValues).forEach(([field, value]) => {
            handleChange(field, value);
          });
        }}
      >
        <Form.Item label="启用" name="enabled" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="自动播放" name="autoPlay" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="循环播放" name="loop" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="播放速度" name="speed">
          <InputNumber min={0.1} max={5} step={0.1} />
        </Form.Item>
      </Form>
    </Card>
  );
};

export default AnimationEditor;
