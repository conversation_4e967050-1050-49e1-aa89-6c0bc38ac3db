/**
 * 子片段编辑器
 */
import React from 'react';
import { Card, Form, InputNumber, Switch, Select, Input, Typography } from 'antd';

const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { Text } = Typography;
const { TextArea } = Input;

/**
 * 子片段编辑器属性
 */
interface SubClipEditorProps {
  /** 子片段 */
  subClips: Record<string, any>;
  /** 选中的子片段名称 */
  selectedSubClip: string | null;
  /** 子片段更新回调 */
  onSubClipUpdate: (name: string, subClip: any) => void;
}

/**
 * 子片段编辑器
 */
export const SubClipEditor: React.FC<SubClipEditorProps> = ({
  subClips,
  selectedSubClip,
  onSubClipUpdate
}) => {
  const [form] = Form.useForm();

  // 当前选中的子片段
  const currentSubClip = selectedSubClip ? subClips[selectedSubClip] : null;

  // 更新子片段属性
  const handleSubClipChange = (field: string, value: any) => {
    if (selectedSubClip && currentSubClip) {
      const updatedSubClip = {
        ...currentSubClip,
        [field]: value
      };
      onSubClipUpdate(selectedSubClip, updatedSubClip);
    }
  };

  // 如果没有选中子片段，显示提示
  if (!selectedSubClip || !currentSubClip) {
    return (
      <Card title="子片段编辑器" size="small">
        <Text type="secondary">请选择一个子片段进行编辑</Text>
      </Card>
    );
  }

  return (
    <Card title="子片段编辑器" size="small">
      <Form
        form={form}
        layout="vertical"
        initialValues={currentSubClip}
        onValuesChange={(changedValues) => {
          Object.entries(changedValues).forEach(([field, value]) => {
            handleSubClipChange(field, value);
          });
        }}
      >
        <Form.Item label="名称" name="name">
          <Input
            value={currentSubClip.name}
            onChange={(e) => handleSubClipChange('name', e.target.value)}
          />
        </Form.Item>

        <Form.Item label="开始时间 (秒)" name="startTime">
          <InputNumber
            min={0}
            step={0.1}
            value={currentSubClip.startTime || 0}
            onChange={(value) => handleSubClipChange('startTime', value || 0)}
          />
        </Form.Item>

        <Form.Item label="结束时间 (秒)" name="endTime">
          <InputNumber
            min={0}
            step={0.1}
            value={currentSubClip.endTime || 5}
            onChange={(value) => handleSubClipChange('endTime', value || 5)}
          />
        </Form.Item>

        <Form.Item label="循环" name="loop" valuePropName="checked">
          <Switch
            checked={currentSubClip.loop || false}
            onChange={(checked) => handleSubClipChange('loop', checked)}
          />
        </Form.Item>

        <Form.Item label="自动播放" name="autoPlay" valuePropName="checked">
          <Switch
            checked={currentSubClip.autoPlay || false}
            onChange={(checked) => handleSubClipChange('autoPlay', checked)}
          />
        </Form.Item>

        <Form.Item label="播放速度" name="playbackSpeed">
          <InputNumber
            min={0.1}
            max={5.0}
            step={0.1}
            value={currentSubClip.playbackSpeed || 1.0}
            onChange={(value) => handleSubClipChange('playbackSpeed', value || 1.0)}
          />
        </Form.Item>

        <Form.Item label="循环模式" name="loopMode">
          <Select
            value={currentSubClip.loopMode || 'repeat'}
            onChange={(value) => handleSubClipChange('loopMode', value)}
          >
            <Option value="once">播放一次</Option>
            <Option value="repeat">重复播放</Option>
            <Option value="pingpong">来回播放</Option>
          </Select>
        </Form.Item>

        <Form.Item label="混合权重" name="blendWeight">
          <InputNumber
            min={0}
            max={1}
            step={0.01}
            value={currentSubClip.blendWeight || 1.0}
            onChange={(value) => handleSubClipChange('blendWeight', value || 1.0)}
          />
        </Form.Item>

        <Form.Item label="优先级" name="priority">
          <InputNumber
            min={0}
            max={100}
            value={currentSubClip.priority || 0}
            onChange={(value) => handleSubClipChange('priority', value || 0)}
          />
        </Form.Item>

        <Form.Item label="描述" name="description">
          <TextArea
            rows={3}
            value={currentSubClip.description || ''}
            onChange={(e) => handleSubClipChange('description', e.target.value)}
            placeholder="输入子片段描述..."
          />
        </Form.Item>
      </Form>

      <div style={{ marginTop: 16 }}>
        <Text strong>子片段信息:</Text>
        <div style={{ marginTop: 8 }}>
          <Text>名称: {currentSubClip.name}</Text><br />
          <Text>持续时间: {((currentSubClip.endTime || 5) - (currentSubClip.startTime || 0)).toFixed(2)}秒</Text><br />
          <Text>循环: {currentSubClip.loop ? '是' : '否'}</Text><br />
          <Text>播放速度: {(currentSubClip.playbackSpeed || 1.0).toFixed(2)}x</Text><br />
          <Text>混合权重: {(currentSubClip.blendWeight || 1.0).toFixed(2)}</Text>
        </div>
      </div>
    </Card>
  );
};

export default SubClipEditor;
