/**
 * AI冲突解析器面板组件
 * 用于显示AI生成的冲突解决方案
 */
import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
  Card,
  List,
  Button,
  Space,
  Tooltip,
  Divider,
  Typography,
  Tag,
  Alert,
  Switch,
  Slider,
  Empty,
  Badge,
  Progress,
  Collapse,
  Tabs,
  Select,
  Spin} from 'antd';
import {
  RobotOutlined,
  CheckOutlined,
  CloseOutlined,
  SettingOutlined,
  SyncOutlined,
  InfoCircleOutlined,
  BulbOutlined,
  ExperimentOutlined,
  ApiOutlined,
  LoadingOutlined,
  QuestionCircleOutlined,
  CodeOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { RootState, store } from '../../store';
import {
  AIResolution,
  AIModelType,
  AIResolverStatus,
  aiConflictResolverService
} from '../../services/AIConflictResolverService';
import {
  setAIResolverEnabled,
  setDefaultModel,
  setAutoApply,
  setAutoApplyThreshold,
  setConfidenceThreshold,
  setSelectedAIResolutionId,
  clearAppliedAIResolutions
} from '../../store/collaboration/aiResolverSlice';
import { formatDateTime, formatRelativeTime } from '../../utils/formatters';
import JsonView from '../common/JsonView';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';
const { confirm } = Modal;

/**
 * AI冲突解析器面板组件
 */
const AIConflictResolverPanel: React.FC<{ conflictId?: string }> = ({ conflictId }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux获取状态
  const enabled = useSelector((state: RootState) => state.aiResolver.enabled);
  const status = useSelector((state: RootState) => state.aiResolver.status);
  const resolutions = useSelector((state: RootState) => state.aiResolver.resolutions);
  const selectedResolutionId = useSelector((state: RootState) => state.aiResolver.selectedResolutionId);
  const defaultModel = useSelector((state: RootState) => state.aiResolver.defaultModel);
  const autoApply = useSelector((state: RootState) => state.aiResolver.autoApply);
  const autoApplyThreshold = useSelector((state: RootState) => state.aiResolver.autoApplyThreshold);
  const confidenceThreshold = useSelector((state: RootState) => state.aiResolver.confidenceThreshold);

  // 本地状态
  const [activeTab, setActiveTab] = useState<string>('resolutions');
  const [showSettings, setShowSettings] = useState<boolean>(false);
  const [processing, setProcessing] = useState<boolean>(false);
  const [selectedConflictId, setSelectedConflictId] = useState<string | undefined>(conflictId);

  // 过滤解决方案
  const filteredResolutions = selectedConflictId
    ? resolutions.filter(r => r.conflictId === selectedConflictId)
    : resolutions;

  const pendingResolutions = filteredResolutions.filter(r => !r.applied);
  const appliedResolutions = filteredResolutions.filter(r => r.applied);

  // 选中的解决方案
  const selectedResolution = resolutions.find(r => r.id === selectedResolutionId);

  // 处理启用/禁用AI解析器
  const handleToggleEnabled = (checked: boolean) => {
    dispatch(setAIResolverEnabled(checked));
    aiConflictResolverService.setEnabled(checked);
  };

  // 处理默认模型变更
  const handleModelChange = (value: AIModelType) => {
    dispatch(setDefaultModel(value));
    aiConflictResolverService.updateConfig({ defaultModel: value });
  };

  // 处理自动应用变更
  const handleAutoApplyChange = (checked: boolean) => {
    dispatch(setAutoApply(checked));
    aiConflictResolverService.updateConfig({ autoApply: checked });
  };

  // 处理自动应用阈值变更
  const handleAutoApplyThresholdChange = (value: number) => {
    dispatch(setAutoApplyThreshold(value));
    aiConflictResolverService.updateConfig({ autoApplyThreshold: value });
  };

  // 处理置信度阈值变更
  const handleConfidenceThresholdChange = (value: number) => {
    dispatch(setConfidenceThreshold(value));
    aiConflictResolverService.updateConfig({ confidenceThreshold: value });
  };

  // 处理选择解决方案
  const handleSelectResolution = (id: string) => {
    dispatch(setSelectedAIResolutionId(id));
  };

  // 处理应用解决方案
  const handleApplyResolution = (id: string) => {
    confirm({
      title: t('collaboration.aiResolver.confirmApply'),
      icon: <QuestionCircleOutlined />,
      content: t('collaboration.aiResolver.confirmApplyDescription'),
      onOk() {
        aiConflictResolverService.applyResolution(id);
      }
    });
  };

  // 处理解析冲突
  const handleResolveConflict = async () => {
    if (!selectedConflictId) {
      return;
    }

    // 获取冲突
    const conflict = store.getState().conflict.conflicts.find(c => c.id === selectedConflictId);

    if (!conflict) {
      return;
    }

    setProcessing(true);

    try {
      await aiConflictResolverService.resolveConflict(conflict);
    } catch (error) {
      console.error('解析冲突时出错:', error);
    } finally {
      setProcessing(false);
    }
  };

  // 处理清除已应用的解决方案
  const handleClearApplied = () => {
    dispatch(clearAppliedAIResolutions());
  };

  // 渲染置信度
  const renderConfidence = (confidence: number) => {
    let color = 'green';

    if (confidence < 0.6) {
      color = 'red';
    } else if (confidence < 0.8) {
      color = 'orange';
    }

    return (
      <Tooltip title={`置信度: ${Math.round(confidence * 100)}%`}>
        <Progress
          percent={Math.round(confidence * 100)}
          size="small"
          strokeColor={color}
          showInfo={false}
        />
      </Tooltip>
    );
  };

  // 渲染模型类型标签
  const renderModelTypeTag = (type: AIModelType) => {
    let color = 'blue';
    let text = '基础模型';

    switch (type) {
      case AIModelType.ADVANCED:
        color = 'green';
        text = '高级模型';
        break;

      case AIModelType.EXPERT:
        color = 'purple';
        text = '专家模型';
        break;

      case AIModelType.CUSTOM:
        color = 'orange';
        text = '自定义模型';
        break;
    }

    return <Tag color={color}>{text}</Tag>;
  };

  // 渲染解决方案项
  const renderResolutionItem = (resolution: AIResolution) => {
    const { id, modelType, confidence, explanation, createdAt, applied, appliedAt } = resolution;
    const isSelected = id === selectedResolutionId;

    return (
      <List.Item
        key={id}
        className={`resolution-item ${isSelected ? 'selected' : ''} ${applied ? 'applied' : ''}`}
        actions={[
          <Button
            key="view"
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleSelectResolution(id)}
          />,
          !applied && (
            <Button
              key="apply"
              type="text"
              icon={<CheckOutlined />}
              onClick={() => handleApplyResolution(id)}
            />
          )
        ]}
      >
        <List.Item.Meta
          avatar={
            <Badge status={applied ? 'success' : 'processing'} />
          }
          title={
            <Space>
              {renderModelTypeTag(modelType)}
              <Text strong>
                {explanation.length > 30 ? explanation.substring(0, 30) + '...' : explanation}
              </Text>
            </Space>
          }
          description={
            <Space direction="vertical" style={{ width: '100%' }}>
              {renderConfidence(confidence)}
              <Text type="secondary">
                {formatRelativeTime(createdAt)}
                {applied && appliedAt && (
                  <span> · 已应用于 {formatRelativeTime(appliedAt)}</span>
                )}
              </Text>
            </Space>
          }
        />
      </List.Item>
    );
  };

  // 渲染设置面板
  const renderSettingsPanel = () => {
    return (
      <div className="settings-panel">
        <Title level={5}>{t('collaboration.aiResolver.settings')}</Title>

        <div className="setting-item">
          <Text>{t('collaboration.aiResolver.enableAI')}</Text>
          <Switch
            checked={enabled}
            onChange={handleToggleEnabled}
          />
        </div>

        <div className="setting-item">
          <Text>{t('collaboration.aiResolver.defaultModel')}</Text>
          <Select
            value={defaultModel}
            onChange={handleModelChange}
            disabled={!enabled}
            style={{ width: 150 }}
          >
            <Option value={AIModelType.BASIC}>{t('collaboration.aiResolver.basicModel')}</Option>
            <Option value={AIModelType.ADVANCED}>{t('collaboration.aiResolver.advancedModel')}</Option>
            <Option value={AIModelType.EXPERT}>{t('collaboration.aiResolver.expertModel')}</Option>
            <Option value={AIModelType.CUSTOM}>{t('collaboration.aiResolver.customModel')}</Option>
          </Select>
        </div>

        <div className="setting-item">
          <Text>{t('collaboration.aiResolver.autoApply')}</Text>
          <Switch
            checked={autoApply}
            onChange={handleAutoApplyChange}
            disabled={!enabled}
          />
        </div>

        <div className="setting-item">
          <Text>{t('collaboration.aiResolver.autoApplyThreshold')}</Text>
          <Slider
            min={0.5}
            max={1}
            step={0.05}
            value={autoApplyThreshold}
            onChange={handleAutoApplyThresholdChange}
            disabled={!enabled || !autoApply}
            tooltip={{ formatter: (value) => `${Math.round(value * 100)}%` }}
            style={{ width: 150 }}
          />
        </div>

        <div className="setting-item">
          <Text>{t('collaboration.aiResolver.confidenceThreshold')}</Text>
          <Slider
            min={0.1}
            max={1}
            step={0.05}
            value={confidenceThreshold}
            onChange={handleConfidenceThresholdChange}
            disabled={!enabled}
            tooltip={{ formatter: (value) => `${Math.round(value * 100)}%` }}
            style={{ width: 150 }}
          />
        </div>
      </div>
    );
  };

  // 渲染选中的解决方案详情
  const renderSelectedResolutionDetails = () => {
    if (!selectedResolution) {
      return (
        <Empty description={t('collaboration.aiResolver.noSelectedResolution')} />
      );
    }

    const { modelType, confidence, explanation, createdAt, applied, appliedAt, mergeStrategy } = selectedResolution;

    return (
      <div className="selected-resolution-details">
        <Alert
          type={applied ? 'success' : 'info'}
          message={
            <Space>
              {renderModelTypeTag(modelType)}
              <Text strong>
                {t('collaboration.aiResolver.aiSolution')}
              </Text>
              {applied && (
                <Tag color="green">{t('collaboration.aiResolver.applied')}</Tag>
              )}
            </Space>
          }
          description={
            <Space direction="vertical" style={{ width: '100%' }}>
              <div className="resolution-confidence">
                <Text>{t('collaboration.aiResolver.confidence')}: {Math.round(confidence * 100)}%</Text>
                {renderConfidence(confidence)}
              </div>

              <div className="resolution-explanation">
                <Text>{explanation}</Text>
              </div>

              <div className="resolution-strategy">
                <Text type="secondary">{t('collaboration.aiResolver.mergeStrategy')}: {mergeStrategy}</Text>
              </div>

              <div className="resolution-time">
                <Text type="secondary">
                  {t('collaboration.aiResolver.createdAt')}: {formatDateTime(createdAt)}
                </Text>
                {applied && appliedAt && (
                  <div>
                    <Text type="secondary">
                      {t('collaboration.aiResolver.appliedAt')}: {formatDateTime(appliedAt)}
                    </Text>
                  </div>
                )}
              </div>
            </Space>
          }
          showIcon
        />

        <Divider>{t('collaboration.aiResolver.resolvedData')}</Divider>

        <div className="resolution-data">
          <JsonView data={selectedResolution.resolvedData} />
        </div>

        {!applied && (
          <div className="resolution-actions">
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={() => handleApplyResolution(selectedResolution.id)}
            >
              {t('collaboration.aiResolver.applyResolution')}
            </Button>
          </div>
        )}
      </div>
    );
  };

  return (
    <Card
      title={
        <Space>
          <RobotOutlined />
          <span>{t('collaboration.aiResolver.title')}</span>
          {status === AIResolverStatus.PROCESSING && (
            <Spin indicator={<LoadingOutlined style={{ fontSize: 16 }} spin />} />
          )}
        </Space>
      }
      extra={
        <Space>
          <Tooltip title={t('collaboration.aiResolver.settings')}>
            <Button
              type={showSettings ? 'primary' : 'default'}
              shape="circle"
              icon={<SettingOutlined />}
              onClick={() => setShowSettings(!showSettings)}
            />
          </Tooltip>
          {appliedResolutions.length > 0 && (
            <Button
              size="small"
              onClick={handleClearApplied}
            >
              {t('collaboration.aiResolver.clearApplied')}
            </Button>
          )}
        </Space>
      }
      className="ai-resolver-panel"
    >
      {showSettings && (
        <>
          {renderSettingsPanel()}
          <Divider />
        </>
      )}

      <div className="conflict-selector">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>{t('collaboration.aiResolver.selectConflict')}</Text>
          <Space>
            <Select
              value={selectedConflictId}
              onChange={setSelectedConflictId}
              placeholder={t('collaboration.aiResolver.selectConflictPlaceholder')}
              style={{ width: 300 }}
              allowClear
            >
              {store.getState().conflict.conflicts
                .filter(c => c.status !== 'resolved')
                .map(conflict => (
                  <Option key={conflict.id} value={conflict.id}>
                    {conflict.type} - {conflict.userName} ({formatRelativeTime(conflict.timestamp)})
                  </Option>
                ))}
            </Select>
            <Button
              type="primary"
              icon={<BulbOutlined />}
              onClick={handleResolveConflict}
              disabled={!selectedConflictId || !enabled || processing}
              loading={processing}
            >
              {t('collaboration.aiResolver.resolveConflict')}
            </Button>
          </Space>
        </Space>
      </div>

      <Divider />

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <BulbOutlined />
              {t('collaboration.aiResolver.resolutions')}
              {pendingResolutions.length > 0 && (
                <Badge count={pendingResolutions.length} style={{ marginLeft: 8 }} />
              )}
            </span>
          }
          key="resolutions"
        >
          {pendingResolutions.length > 0 ? (
            <List
              dataSource={pendingResolutions}
              renderItem={renderResolutionItem}
              className="resolution-list"
            />
          ) : (
            <Empty description={t('collaboration.aiResolver.noResolutions')} />
          )}
        </TabPane>

        <TabPane
          tab={
            <span>
              <CheckOutlined />
              {t('collaboration.aiResolver.applied')}
              {appliedResolutions.length > 0 && (
                <Badge count={appliedResolutions.length} style={{ marginLeft: 8 }} />
              )}
            </span>
          }
          key="applied"
        >
          {appliedResolutions.length > 0 ? (
            <List
              dataSource={appliedResolutions}
              renderItem={renderResolutionItem}
              className="resolution-list"
            />
          ) : (
            <Empty description={t('collaboration.aiResolver.noAppliedResolutions')} />
          )}
        </TabPane>

        <TabPane
          tab={
            <span>
              <InfoCircleOutlined />
              {t('collaboration.aiResolver.details')}
            </span>
          }
          key="details"
        >
          {renderSelectedResolutionDetails()}
        </TabPane>
      </Tabs>

      <style jsx>{`
        .ai-resolver-panel {
          margin-bottom: 16px;
        }

        .settings-panel {
          padding: 8px;
          background-color: #f7f7f7;
          border-radius: 4px;
        }

        .setting-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
        }

        .conflict-selector {
          margin-top: 16px;
        }

        .resolution-list {
          max-height: 400px;
          overflow-y: auto;
        }

        .resolution-item {
          border-left: 3px solid transparent;
          transition: all 0.3s;
        }

        .resolution-item:hover {
          background-color: #f5f5f5;
        }

        .resolution-item.selected {
          border-left-color: #1890ff;
          background-color: #e6f7ff;
        }

        .resolution-item.applied {
          opacity: 0.7;
        }

        .selected-resolution-details {
          padding: 8px;
        }

        .resolution-data {
          margin-top: 16px;
          max-height: 400px;
          overflow-y: auto;
        }

        .resolution-actions {
          margin-top: 16px;
          display: flex;
          justify-content: flex-end;
        }

        .resolution-confidence, .resolution-explanation, .resolution-strategy, .resolution-time {
          margin-bottom: 8px;
        }
      `}</style>
    </Card>
  );
}

export default AIConflictResolverPanel;