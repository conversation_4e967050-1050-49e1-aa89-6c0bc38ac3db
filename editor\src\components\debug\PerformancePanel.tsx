/**
 * 性能分析面板组件
 * 用于显示和分析引擎性能指标
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Tabs, Statistic, Button, Switch, Select, Divider, Space, Typography, Table, Progress, Empty, List, Tag, Badge } from 'antd';
import { useTranslation } from 'react-i18next';
import { Line } from '@ant-design/charts';
import {
  ReloadOutlined,
  PauseOutlined,
  PlayCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  DashboardOutlined,
  AreaChartOutlined,
  BarChartOutlined,
  LineChartOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  SwapOutlined,
  SaveOutlined} from '@ant-design/icons';
// 移除引擎直接导入
import { useAppDispatch, useAppSelector } from '../../store';
import { setPerformanceMonitoringEnabled } from '../../store/debug/debugSlice';
import './PerformancePanel.less';

const { TabPane } = Tabs;
const { Title, Text } = Typography;
const { Option } = Select;
import { Select, Switch, InputNumber, Form } from 'antd';

// 格式化数值，保留两位小数
const formatNumber = (value: number): string => {
  return value.toFixed(2);
};

// 格式化时间，根据值的大小选择合适的单位
const formatTime = (value: number): string => {
  if (value < 1) {
    return `${(value * 1000).toFixed(2)} μs`;
  } else if (value < 1000) {
    return `${value.toFixed(2)} ms`;
  } else {
    return `${(value / 1000).toFixed(2)} s`;
  }
};

// 格式化百分比变化
const formatPercentChange = (value: number): string => {
  return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
};

// 获取性能指标状态颜色
const getMetricStatusColor = (value: number, threshold: number, isHigherBetter: boolean = false): string => {
  if (isHigherBetter) {
    return value >= threshold ? '#52c41a' : '#f5222d';
  } else {
    return value <= threshold ? '#52c41a' : '#f5222d';
  }
};

interface PerformancePanelProps {
  className?: string;
}

/**
 * 性能分析面板组件
 */
const PerformancePanel: React.FC<PerformancePanelProps> = ({ className }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { performanceMonitoringEnabled } = useAppSelector((state) => state.debug);

  // 状态
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [isMonitoring, setIsMonitoring] = useState<boolean>(false);
  const [sampleInterval, setSampleInterval] = useState<number>(1000);
  const [historyLength, setHistoryLength] = useState<number>(60);
  const [performanceReport, setPerformanceReport] = useState<PerformanceReport | null>(null);
  const [fpsHistory, setFpsHistory] = useState<{ time: string; value: number }[]>([]);
  const [memoryHistory, setMemoryHistory] = useState<{ time: string; value: number }[]>([]);
  const [renderTimeHistory, setRenderTimeHistory] = useState<{ time: string; value: number }[]>([]);
  const [cpuHistory, setcpuHistory] = useState<{ time: string; value: number }[]>([]);
  const [gpuHistory, setGpuHistory] = useState<{ time: string; value: number }[]>([]);
  const [bottlenecks, setBottlenecks] = useState<any[]>([]);
  const [trends, setTrends] = useState<any[]>([]);
  const [optimizationSuggestions, setOptimizationSuggestions] = useState<any[]>([]);
  const [showSettings, setShowSettings] = useState<boolean>(false);
  const [enableBottleneckDetection, setEnableBottleneckDetection] = useState<boolean>(true);
  const [enableTrendAnalysis, setEnableTrendAnalysis] = useState<boolean>(true);
  const [enablePerformanceScoring, setEnablePerformanceScoring] = useState<boolean>(true);
  const [enableOptimizationSuggestions, setEnableOptimizationSuggestions] = useState<boolean>(true);
  const [collectGPUMetrics, setCollectGPUMetrics] = useState<boolean>(true);
  const [collectCPUMetrics, setCollectCPUMetrics] = useState<boolean>(true);
  const [collectNetworkMetrics, setCollectNetworkMetrics] = useState<boolean>(true);
  const [collectResourceMetrics, setCollectResourceMetrics] = useState<boolean>(true);

  // 定时器引用
  const timerRef = useRef<number | null>(null);

  // 初始化和清理
  useEffect(() => {
    if (performanceMonitoringEnabled) {
      startMonitoring();
    }

    return () => {
      stopMonitoring();
    };
  }, [performanceMonitoringEnabled]);

  // 启动监控
  const startMonitoring = () => {
    if (isMonitoring) return;

    // 配置性能监控器
    PerformanceMonitor.getInstance().configure({
      enabled: true,
      sampleInterval,
      historyLimit: historyLength,
      autoSample: true,
      debug: false,
      enableWarnings: true,
      collectRenderMetrics: true,
      collectPhysicsMetrics: true,
      collectMemoryMetrics: true,
      collectSystemMetrics: true,
      collectGPUMetrics,
      collectCPUMetrics,
      collectNetworkMetrics,
      collectResourceMetrics,
      collectEventMetrics: true,
      collectGCMetrics: true,
      enableBottleneckDetection,
      enableTrendAnalysis,
      enablePerformanceScoring,
      enableOptimizationSuggestions,
      enableAutoOptimization: false,
      enableReportExport: false});

    // 启动性能监控器
    PerformanceMonitor.start();

    // 设置定时器定期获取性能报告
    timerRef.current = window.setInterval(() => {
      updatePerformanceData();
    }, sampleInterval);

    setIsMonitoring(true);
    dispatch(setPerformanceMonitoringEnabled(true));
  };

  // 停止监控
  const stopMonitoring = () => {
    if (!isMonitoring) return;

    // 停止性能监控器
    PerformanceMonitor.stop();

    // 清除定时器
    if (timerRef.current !== null) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    setIsMonitoring(false);
    dispatch(setPerformanceMonitoringEnabled(false));
  };

  // 更新性能数据
  const updatePerformanceData = () => {
    const report = PerformanceMonitor.getReport();
    setPerformanceReport(report);

    // 更新历史数据
    const now = new Date().toLocaleTimeString();

    // FPS历史
    if (report.metrics[PerformanceMetricType.FPS]) {
      setFpsHistory(prev => {
        const newHistory = [...prev, { time: now, value: report.metrics[PerformanceMetricType.FPS].value }];
        if (newHistory.length > historyLength) {
          return newHistory.slice(-historyLength);
        }
        return newHistory;
      });
    }

    // 内存使用历史
    if (report.metrics[PerformanceMetricType.MEMORY_USAGE]) {
      setMemoryHistory(prev => {
        const newHistory = [...prev, { time: now, value: report.metrics[PerformanceMetricType.MEMORY_USAGE].value }];
        if (newHistory.length > historyLength) {
          return newHistory.slice(-historyLength);
        }
        return newHistory;
      });
    }

    // 渲染时间历史
    if (report.metrics[PerformanceMetricType.RENDER_TIME]) {
      setRenderTimeHistory(prev => {
        const newHistory = [...prev, { time: now, value: report.metrics[PerformanceMetricType.RENDER_TIME].value }];
        if (newHistory.length > historyLength) {
          return newHistory.slice(-historyLength);
        }
        return newHistory;
      });
    }

    // CPU使用率历史
    if (report.metrics[PerformanceMetricType.CPU_USAGE]) {
      setcpuHistory(prev => {
        const newHistory = [...prev, { time: now, value: report.metrics[PerformanceMetricType.CPU_USAGE].value }];
        if (newHistory.length > historyLength) {
          return newHistory.slice(-historyLength);
        }
        return newHistory;
      });
    }

    // GPU使用率历史
    if (report.metrics[PerformanceMetricType.GPU_USAGE]) {
      setGpuHistory(prev => {
        const newHistory = [...prev, { time: now, value: report.metrics[PerformanceMetricType.GPU_USAGE].value }];
        if (newHistory.length > historyLength) {
          return newHistory.slice(-historyLength);
        }
        return newHistory;
      });
    }

    // 更新瓶颈信息
    if (report.bottlenecks && report.bottlenecks.length > 0) {
      setBottlenecks(report.bottlenecks);
    } else {
      setBottlenecks([]);
    }

    // 更新趋势信息
    if (report.trends && report.trends.length > 0) {
      setTrends(report.trends);
    } else {
      setTrends([]);
    }

    // 生成优化建议
    if (enableOptimizationSuggestions && report.bottlenecks && report.bottlenecks.length > 0) {
      const suggestions = generateOptimizationSuggestions(report);
      if (suggestions.length > 0) {
        setOptimizationSuggestions(suggestions);
      }
    }
  };

  // 生成优化建议
  const generateOptimizationSuggestions = (report: PerformanceReport): any[] => {
    const suggestions: any[] = [];

    // 从瓶颈生成建议
    if (report.bottlenecks) {
      report.bottlenecks.forEach(bottleneck => {
        if (bottleneck.optimizationSuggestions && bottleneck.optimizationSuggestions.length > 0) {
          // 只添加严重程度较高的瓶颈建议
          if (bottleneck.severity > 0.5) {
            suggestions.push({
              id: `${bottleneck.type}-${Date.now()}`,
              type: bottleneck.type,
              title: getBottleneckTitle(bottleneck.type),
              description: bottleneck.description,
              priority: getSuggestionPriority(bottleneck.severity),
              implementationSteps: bottleneck.optimizationSuggestions,
              expectedImprovement: getExpectedImprovement(bottleneck.type, bottleneck.severity),
              relatedMetrics: bottleneck.relatedMetrics,
              applied: false,
              createdAt: Date.now()
            });
          }
        }
      });
    }

    return suggestions;
  };

  // 获取瓶颈标题
  const getBottleneckTitle = (type: string): string => {
    switch (type) {
      case PerformanceBottleneckType.CPU:
        return t('debug.performance.bottleneck.cpuTitle');
      case PerformanceBottleneckType.GPU:
        return t('debug.performance.bottleneck.gpuTitle');
      case PerformanceBottleneckType.MEMORY:
        return t('debug.performance.bottleneck.memoryTitle');
      case PerformanceBottleneckType.NETWORK:
        return t('debug.performance.bottleneck.networkTitle');
      case PerformanceBottleneckType.RENDERING:
        return t('debug.performance.bottleneck.renderingTitle');
      case PerformanceBottleneckType.PHYSICS:
        return t('debug.performance.bottleneck.physicsTitle');
      case PerformanceBottleneckType.SCRIPT:
        return t('debug.performance.bottleneck.scriptTitle');
      case PerformanceBottleneckType.RESOURCES:
        return t('debug.performance.bottleneck.resourcesTitle');
      default:
        return t('debug.performance.bottleneck.unknownTitle');
    }
  };

  // 获取建议优先级
  const getSuggestionPriority = (severity: number): string => {
    if (severity > 0.8) {
      return 'high';
    } else if (severity > 0.5) {
      return 'medium';
    } else {
      return 'low';
    }
  };

  // 获取预期改进
  const getExpectedImprovement = (type: string, severity: number): string => {
    const improvementPercent = Math.round(severity * 100 * 0.7); // 假设可以改进70%的问题

    switch (type) {
      case PerformanceBottleneckType.CPU:
        return t('debug.performance.improvement.cpu', { percent: improvementPercent });
      case PerformanceBottleneckType.GPU:
        return t('debug.performance.improvement.gpu', { percent: improvementPercent });
      case PerformanceBottleneckType.MEMORY:
        return t('debug.performance.improvement.memory', { percent: improvementPercent });
      case PerformanceBottleneckType.NETWORK:
        return t('debug.performance.improvement.network', { percent: improvementPercent });
      case PerformanceBottleneckType.RENDERING:
        return t('debug.performance.improvement.rendering', { percent: improvementPercent });
      case PerformanceBottleneckType.PHYSICS:
        return t('debug.performance.improvement.physics', { percent: improvementPercent });
      case PerformanceBottleneckType.SCRIPT:
        return t('debug.performance.improvement.script', { percent: improvementPercent });
      case PerformanceBottleneckType.RESOURCES:
        return t('debug.performance.improvement.resources', { percent: improvementPercent });
      default:
        return t('debug.performance.improvement.unknown', { percent: improvementPercent });
    }
  };

  // 重置性能数据
  const resetPerformanceData = () => {
    setFpsHistory([]);
    setMemoryHistory([]);
    setRenderTimeHistory([]);
    setcpuHistory([]);
    setGpuHistory([]);
    setBottlenecks([]);
    setTrends([]);
    setOptimizationSuggestions([]);
    PerformanceMonitor.getInstance().clearAllHistory();
  };

  // 切换监控状态
  const toggleMonitoring = () => {
    if (isMonitoring) {
      stopMonitoring();
    } else {
      startMonitoring();
    }
  };

  // 渲染工具栏
  const renderToolbar = () => {
    return (
      <div className="performance-toolbar">
        <Space>
          <Button
            type={isMonitoring ? 'primary' : 'default'}
            icon={isMonitoring ? <PauseOutlined /> : <PlayCircleOutlined />}
            onClick={toggleMonitoring}
          >
            {isMonitoring ? t('debug.performance.stopMonitoring') : t('debug.performance.startMonitoring')}
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={resetPerformanceData}
            disabled={!isMonitoring}
          >
            {t('debug.performance.reset')}
          </Button>
          <Button
            icon={<SettingOutlined />}
            onClick={() => setShowSettings(!showSettings)}
          >
            {t('debug.performance.settings')}
          </Button>
          <Divider type="vertical" />
          <Text>{t('debug.performance.sampleInterval')}:</Text>
          <Select
            value={sampleInterval}
            onChange={(value) => setSampleInterval(value)}
            style={{ width: 120 }}
            disabled={isMonitoring}
          >
            <Option value={500}>500ms</Option>
            <Option value={1000}>1000ms</Option>
            <Option value={2000}>2000ms</Option>
            <Option value={5000}>5000ms</Option>
          </Select>
          <Text>{t('debug.performance.historyLength')}:</Text>
          <Select
            value={historyLength}
            onChange={(value) => setHistoryLength(value)}
            style={{ width: 120 }}
            disabled={isMonitoring}
          >
            <Option value={30}>30</Option>
            <Option value={60}>60</Option>
            <Option value={120}>120</Option>
            <Option value={300}>300</Option>
          </Select>
        </Space>

        {showSettings && (
          <div className="performance-settings">
            <Divider>{t('debug.performance.advancedSettings')}</Divider>
            <Row gutter={[16, 8]}>
              <Col span={12}>
                <Space>
                  <Switch
                    checked={enableBottleneckDetection}
                    onChange={setEnableBottleneckDetection}
                    disabled={isMonitoring}
                  />
                  <Text>{t('debug.performance.enableBottleneckDetection')}</Text>
                </Space>
              </Col>
              <Col span={12}>
                <Space>
                  <Switch
                    checked={enableTrendAnalysis}
                    onChange={setEnableTrendAnalysis}
                    disabled={isMonitoring}
                  />
                  <Text>{t('debug.performance.enableTrendAnalysis')}</Text>
                </Space>
              </Col>
              <Col span={12}>
                <Space>
                  <Switch
                    checked={enablePerformanceScoring}
                    onChange={setEnablePerformanceScoring}
                    disabled={isMonitoring}
                  />
                  <Text>{t('debug.performance.enablePerformanceScoring')}</Text>
                </Space>
              </Col>
              <Col span={12}>
                <Space>
                  <Switch
                    checked={enableOptimizationSuggestions}
                    onChange={setEnableOptimizationSuggestions}
                    disabled={isMonitoring}
                  />
                  <Text>{t('debug.performance.enableOptimizationSuggestions')}</Text>
                </Space>
              </Col>
              <Col span={12}>
                <Space>
                  <Switch
                    checked={collectGPUMetrics}
                    onChange={setCollectGPUMetrics}
                    disabled={isMonitoring}
                  />
                  <Text>{t('debug.performance.collectGPUMetrics')}</Text>
                </Space>
              </Col>
              <Col span={12}>
                <Space>
                  <Switch
                    checked={collectCPUMetrics}
                    onChange={setCollectCPUMetrics}
                    disabled={isMonitoring}
                  />
                  <Text>{t('debug.performance.collectCPUMetrics')}</Text>
                </Space>
              </Col>
              <Col span={12}>
                <Space>
                  <Switch
                    checked={collectNetworkMetrics}
                    onChange={setCollectNetworkMetrics}
                    disabled={isMonitoring}
                  />
                  <Text>{t('debug.performance.collectNetworkMetrics')}</Text>
                </Space>
              </Col>
              <Col span={12}>
                <Space>
                  <Switch
                    checked={collectResourceMetrics}
                    onChange={setCollectResourceMetrics}
                    disabled={isMonitoring}
                  />
                  <Text>{t('debug.performance.collectResourceMetrics')}</Text>
                </Space>
              </Col>
            </Row>
          </div>
        )}
      </div>
    );
  };

  // 渲染指标标签页
  const renderMetricsTab = () => {
    if (!performanceReport) {
      return (
        <div className="performance-empty">
          <Text>{t('debug.performance.noData')}</Text>
        </div>
      );
    }

    const { metrics } = performanceReport;

    // 按类别分组指标
    const renderingMetrics = [
      PerformanceMetricType.FPS,
      PerformanceMetricType.RENDER_TIME,
      PerformanceMetricType.DRAW_CALLS,
      PerformanceMetricType.TRIANGLES,
      PerformanceMetricType.VERTICES,
      PerformanceMetricType.VISIBLE_OBJECT_COUNT,
      PerformanceMetricType.CULLED_OBJECT_COUNT,
      PerformanceMetricType.LIGHT_COUNT,
      PerformanceMetricType.SHADOW_MAP_COUNT,
      PerformanceMetricType.POST_PROCESS_PASS_COUNT,
      PerformanceMetricType.POST_PROCESS_TIME,
    ];

    const memoryMetrics = [
      PerformanceMetricType.MEMORY_USAGE,
      PerformanceMetricType.TEXTURE_MEMORY,
      PerformanceMetricType.GEOMETRY_MEMORY,
      PerformanceMetricType.GPU_MEMORY,
      PerformanceMetricType.RESOURCE_MEMORY,
    ];

    const systemMetrics = [
      PerformanceMetricType.CPU_USAGE,
      PerformanceMetricType.GPU_USAGE,
      PerformanceMetricType.ENTITY_COUNT,
      PerformanceMetricType.COMPONENT_COUNT,
      PerformanceMetricType.SYSTEM_COUNT,
      PerformanceMetricType.SCRIPT_TIME,
      PerformanceMetricType.TOTAL_UPDATE_TIME,
    ];

    const physicsMetrics = [
      PerformanceMetricType.PHYSICS_TIME,
      PerformanceMetricType.COLLISION_PAIRS,
      PerformanceMetricType.CONTACT_POINTS,
    ];

    const networkMetrics = [
      PerformanceMetricType.NETWORK_TIME,
      PerformanceMetricType.NETWORK_MESSAGE_COUNT,
      PerformanceMetricType.NETWORK_DATA_SIZE,
      PerformanceMetricType.NETWORK_LATENCY,
    ];

    const resourceMetrics = [
      PerformanceMetricType.RESOURCE_LOAD_TIME,
      PerformanceMetricType.RESOURCE_COUNT,
    ];

    // 表格列定义
    const columns = [
      {
        title: t('debug.performance.metric'),
        dataIndex: 'name',
        key: 'name',
        width: '25%'},
      {
        title: t('debug.performance.value'),
        dataIndex: 'value',
        key: 'value',
        render: (value: number, record: any) => (
          <span style={{
            color: record.exceedsThreshold ? '#f5222d' : 'inherit',
            fontWeight: record.exceedsThreshold ? 'bold' : 'normal'
          }}>
            {formatNumber(value)} {record.unit}
          </span>
        )},
      {
        title: t('debug.performance.min'),
        dataIndex: 'min',
        key: 'min',
        render: (value: number, record: any) => `${formatNumber(value)} ${record.unit}`},
      {
        title: t('debug.performance.max'),
        dataIndex: 'max',
        key: 'max',
        render: (value: number, record: any) => `${formatNumber(value)} ${record.unit}`},
      {
        title: t('debug.performance.average'),
        dataIndex: 'average',
        key: 'average',
        render: (value: number, record: any) => `${formatNumber(value)} ${record.unit}`},
      {
        title: t('debug.performance.threshold'),
        dataIndex: 'threshold',
        key: 'threshold',
        render: (value: number, record: any) => value ? `${formatNumber(value)} ${record.unit}` : '-'},
      {
        title: t('debug.performance.chart'),
        dataIndex: 'history',
        key: 'chart',
        render: (history: number[]) => (
          <div style={{ width: 120, height: 30 }}>
            <Line
              data={history.map((value, index) => ({ index, value }))}
              xField="index"
              yField="value"
              padding={0}
              xAxis={false}
              yAxis={false}
              smooth={true}
              animation={false}
              tooltip={false}
              lineStyle={{
                stroke: '#1890ff',
                lineWidth: 2}}
            />
          </div>
        )},
    ];

    // 生成表格数据
    const generateTableData = (metricTypes: PerformanceMetricType[]) => {
      return metricTypes.map(type => {
        const metric = metrics[type];
        if (!metric) return null;

        return {
          key: type,
          type,
          name: t(`debug.performance.${type}`),
          value: metric.value,
          min: metric.min,
          max: metric.max,
          average: metric.average,
          history: metric.history,
          unit: metric.unit || '',
          threshold: metric.threshold,
          exceedsThreshold: metric.exceedsThreshold};
      }).filter(Boolean);
    };

    return (
      <div className="performance-metrics">
        <Tabs defaultActiveKey="rendering">
          <TabPane tab={t('debug.performance.renderingMetrics')} key="rendering">
            <Table
              columns={columns}
              dataSource={generateTableData(renderingMetrics)}
              pagination={false}
              size="small"
              scroll={{ x: 'max-content' }}
            />
          </TabPane>
          <TabPane tab={t('debug.performance.memoryMetrics')} key="memory">
            <Table
              columns={columns}
              dataSource={generateTableData(memoryMetrics)}
              pagination={false}
              size="small"
              scroll={{ x: 'max-content' }}
            />
          </TabPane>
          <TabPane tab={t('debug.performance.systemMetrics')} key="system">
            <Table
              columns={columns}
              dataSource={generateTableData(systemMetrics)}
              pagination={false}
              size="small"
              scroll={{ x: 'max-content' }}
            />
          </TabPane>
          <TabPane tab={t('debug.performance.physicsMetrics')} key="physics">
            <Table
              columns={columns}
              dataSource={generateTableData(physicsMetrics)}
              pagination={false}
              size="small"
              scroll={{ x: 'max-content' }}
            />
          </TabPane>
          <TabPane tab={t('debug.performance.networkMetrics')} key="network">
            <Table
              columns={columns}
              dataSource={generateTableData(networkMetrics)}
              pagination={false}
              size="small"
              scroll={{ x: 'max-content' }}
            />
          </TabPane>
          <TabPane tab={t('debug.performance.resourceMetrics')} key="resource">
            <Table
              columns={columns}
              dataSource={generateTableData(resourceMetrics)}
              pagination={false}
              size="small"
              scroll={{ x: 'max-content' }}
            />
          </TabPane>
        </Tabs>
      </div>
    );
  };

  // 渲染瓶颈标签页
  const renderBottlenecksTab = () => {
    if (!performanceReport) {
      return (
        <div className="performance-empty">
          <Text>{t('debug.performance.noData')}</Text>
        </div>
      );
    }

    const { bottlenecks } = performanceReport;

    if (!bottlenecks || bottlenecks.length === 0) {
      return (
        <div className="performance-empty">
          <Empty
            description={t('debug.performance.noBottlenecks')}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>
      );
    }

    const columns = [
      {
        title: t('debug.performance.bottleneckType'),
        dataIndex: 'type',
        key: 'type',
        render: (type: PerformanceBottleneckType) => {
          switch (type) {
            case PerformanceBottleneckType.CPU:
              return <Tag color="blue">{t('debug.performance.bottleneck.cpu')}</Tag>;
            case PerformanceBottleneckType.GPU:
              return <Tag color="purple">{t('debug.performance.bottleneck.gpu')}</Tag>;
            case PerformanceBottleneckType.MEMORY:
              return <Tag color="orange">{t('debug.performance.bottleneck.memory')}</Tag>;
            case PerformanceBottleneckType.NETWORK:
              return <Tag color="green">{t('debug.performance.bottleneck.network')}</Tag>;
            case PerformanceBottleneckType.RENDERING:
              return <Tag color="red">{t('debug.performance.bottleneck.rendering')}</Tag>;
            case PerformanceBottleneckType.PHYSICS:
              return <Tag color="cyan">{t('debug.performance.bottleneck.physics')}</Tag>;
            case PerformanceBottleneckType.SCRIPT:
              return <Tag color="magenta">{t('debug.performance.bottleneck.script')}</Tag>;
            case PerformanceBottleneckType.RESOURCES:
              return <Tag color="gold">{t('debug.performance.bottleneck.resources')}</Tag>;
            default:
              return <Tag>{t('debug.performance.bottleneck.unknown')}</Tag>;
          }
        }
      },
      {
        title: t('debug.performance.description'),
        dataIndex: 'description',
        key: 'description'},
      {
        title: t('debug.performance.severity'),
        dataIndex: 'severity',
        key: 'severity',
        render: (severity: number) => (
          <Progress
            percent={Math.round(severity * 100)}
            size="small"
            status={severity > 0.7 ? "exception" : severity > 0.4 ? "normal" : "success"}
          />
        )
      },
      {
        title: t('debug.performance.relatedMetrics'),
        dataIndex: 'relatedMetrics',
        key: 'relatedMetrics',
        render: (metrics: string[]) => (
          <>
            {metrics.map(metric => (
              <Tag key={metric}>{t(`debug.performance.${metric}`)}</Tag>
            ))}
          </>
        )
      },
    ];

    return (
      <div className="performance-bottlenecks">
        <Table
          columns={columns}
          dataSource={bottlenecks.map((bottleneck, index) => ({
            key: index,
            ...bottleneck
          }))}
          pagination={false}
          expandable={{
            expandedRowRender: (record) => (
              <div className="bottleneck-suggestions">
                <Title level={5}>{t('debug.performance.optimizationSuggestions')}</Title>
                <ul>
                  {record.optimizationSuggestions.map((suggestion: string, index: number) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              </div>
            )}}
        />
      </div>
    );
  };

  // 渲染趋势标签页
  const renderTrendsTab = () => {
    if (!performanceReport) {
      return (
        <div className="performance-empty">
          <Text>{t('debug.performance.noData')}</Text>
        </div>
      );
    }

    const { trends, metrics } = performanceReport;

    if (!trends || trends.length === 0) {
      return (
        <div className="performance-empty">
          <Empty
            description={t('debug.performance.noTrends')}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>
      );
    }

    // 关键指标历史数据图表
    const keyMetrics = [
      PerformanceMetricType.FPS,
      PerformanceMetricType.MEMORY_USAGE,
      PerformanceMetricType.RENDER_TIME,
      PerformanceMetricType.DRAW_CALLS,
    ];

    return (
      <div className="performance-trends">
        <Title level={4}>{t('debug.performance.keyMetricsTrends')}</Title>

        <Row gutter={[16, 16]}>
          {keyMetrics.map(metricType => {
            const metric = metrics[metricType];
            if (!metric || !metric.history || metric.history.length < 2) return null;

            const chartData = metric.history.map((value, index) => ({
              index,
              value}));

            const config = {
              data: chartData,
              xField: 'index',
              yField: 'value',
              smooth: true,
              meta: {
                value: {
                  alias: t(`debug.performance.${metricType}`)}},
              tooltip: {
                formatter: (datum: any) => {
                  return { name: t(`debug.performance.${metricType}`), value: formatNumber(datum.value) };
                }}};

            return (
              <Col span={12} key={metricType}>
                <Card title={t(`debug.performance.${metricType}`)}>
                  <div style={{ height: 200 }}>
                    <Line {...config} />
                  </div>
                </Card>
              </Col>
            );
          })}
        </Row>

        <Divider>{t('debug.performance.detectedTrends')}</Divider>

        <List
          dataSource={trends}
          renderItem={(trend) => (
            <List.Item>
              <List.Item.Meta
                title={
                  <Space>
                    {trend.type === 'improving' && <ArrowUpOutlined style={{ color: '#52c41a' }} />}
                    {trend.type === 'degrading' && <ArrowDownOutlined style={{ color: '#f5222d' }} />}
                    {trend.type === 'stable' && <MinusOutlined style={{ color: '#1890ff' }} />}
                    {trend.type === 'fluctuating' && <SwapOutlined style={{ color: '#faad14' }} />}
                    {t(`debug.performance.${trend.metricType}`)}
                  </Space>
                }
                description={
                  <div>
                    <Text>{t(`debug.performance.trend.${trend.type}`)}</Text>
                    <br />
                    <Text>{t('debug.performance.changeRate')}: {formatPercentChange(trend.changeRate)}</Text>
                    <br />
                    <Text>{t('debug.performance.duration')}: {formatTime(trend.duration)}</Text>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </div>
    );
  };

  // 渲染优化标签页
  const renderOptimizationTab = () => {
    if (!performanceReport) {
      return (
        <div className="performance-empty">
          <Text>{t('debug.performance.noData')}</Text>
        </div>
      );
    }

    const { bottlenecks } = performanceReport;

    // 获取优化建议
    const optimizationSuggestions = bottlenecks.flatMap(bottleneck =>
      bottleneck.optimizationSuggestions.map(suggestion => ({
        bottleneckType: bottleneck.type,
        suggestion,
        severity: bottleneck.severity}))
    );

    // 按类别分组优化建议
    const renderingSuggestions = optimizationSuggestions.filter(item =>
      item.bottleneckType === PerformanceBottleneckType.RENDERING ||
      item.bottleneckType === PerformanceBottleneckType.GPU
    );

    const memorySuggestions = optimizationSuggestions.filter(item =>
      item.bottleneckType === PerformanceBottleneckType.MEMORY
    );

    const cpuSuggestions = optimizationSuggestions.filter(item =>
      item.bottleneckType === PerformanceBottleneckType.CPU ||
      item.bottleneckType === PerformanceBottleneckType.SCRIPT
    );

    const physicsSuggestions = optimizationSuggestions.filter(item =>
      item.bottleneckType === PerformanceBottleneckType.PHYSICS
    );

    const networkSuggestions = optimizationSuggestions.filter(item =>
      item.bottleneckType === PerformanceBottleneckType.NETWORK
    );

    const resourceSuggestions = optimizationSuggestions.filter(item =>
      item.bottleneckType === PerformanceBottleneckType.RESOURCES
    );

    // 渲染优化建议列表
    const renderSuggestionList = (suggestions: any[]) => {
      if (suggestions.length === 0) {
        return (
          <Empty
            description={t('debug.performance.noSuggestions')}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        );
      }

      return (
        <List
          dataSource={suggestions}
          renderItem={(item) => (
            <List.Item>
              <List.Item.Meta
                avatar={
                  <Tag color={
                    item.severity > 0.7 ? 'red' :
                    item.severity > 0.4 ? 'orange' :
                    'green'
                  }>
                    {Math.round(item.severity * 100)}%
                  </Tag>
                }
                title={item.suggestion}
                description={t(`debug.performance.bottleneck.${item.bottleneckType}`)}
              />
            </List.Item>
          )}
        />
      );
    };

    return (
      <div className="performance-optimization">
        <Tabs defaultActiveKey="rendering">
          <TabPane
            tab={
              <span>
                <Badge count={renderingSuggestions.length} size="small">
                  {t('debug.performance.renderingOptimization')}
                </Badge>
              </span>
            }
            key="rendering"
          >
            {renderSuggestionList(renderingSuggestions)}
          </TabPane>
          <TabPane
            tab={
              <span>
                <Badge count={memorySuggestions.length} size="small">
                  {t('debug.performance.memoryOptimization')}
                </Badge>
              </span>
            }
            key="memory"
          >
            {renderSuggestionList(memorySuggestions)}
          </TabPane>
          <TabPane
            tab={
              <span>
                <Badge count={cpuSuggestions.length} size="small">
                  {t('debug.performance.cpuOptimization')}
                </Badge>
              </span>
            }
            key="cpu"
          >
            {renderSuggestionList(cpuSuggestions)}
          </TabPane>
          <TabPane
            tab={
              <span>
                <Badge count={physicsSuggestions.length} size="small">
                  {t('debug.performance.physicsOptimization')}
                </Badge>
              </span>
            }
            key="physics"
          >
            {renderSuggestionList(physicsSuggestions)}
          </TabPane>
          <TabPane
            tab={
              <span>
                <Badge count={networkSuggestions.length} size="small">
                  {t('debug.performance.networkOptimization')}
                </Badge>
              </span>
            }
            key="network"
          >
            {renderSuggestionList(networkSuggestions)}
          </TabPane>
          <TabPane
            tab={
              <span>
                <Badge count={resourceSuggestions.length} size="small">
                  {t('debug.performance.resourceOptimization')}
                </Badge>
              </span>
            }
            key="resource"
          >
            {renderSuggestionList(resourceSuggestions)}
          </TabPane>
        </Tabs>
      </div>
    );
  };

  // 渲染概览标签页
  const renderOverviewTab = () => {
    if (!performanceReport) {
      return (
        <div className="performance-empty">
          <Text>{t('debug.performance.noData')}</Text>
        </div>
      );
    }

    const { metrics, bottlenecks, trends, overallScore, status } = performanceReport;

    return (
      <div className="performance-overview">
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('debug.performance.fps')}
                value={metrics[PerformanceMetricType.FPS]?.value || 0}
                precision={1}
                valueStyle={{ color: getMetricStatusColor(metrics[PerformanceMetricType.FPS]?.value || 0, 30, true) }}
                prefix={<DashboardOutlined />}
                suffix="FPS"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('debug.performance.memory')}
                value={metrics[PerformanceMetricType.MEMORY_USAGE]?.value || 0}
                precision={1}
                valueStyle={{ color: getMetricStatusColor(metrics[PerformanceMetricType.MEMORY_USAGE]?.value || 0, 500) }}
                prefix={<AreaChartOutlined />}
                suffix="MB"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('debug.performance.renderTime')}
                value={metrics[PerformanceMetricType.RENDER_TIME]?.value || 0}
                precision={2}
                valueStyle={{ color: getMetricStatusColor(metrics[PerformanceMetricType.RENDER_TIME]?.value || 0, 16) }}
                prefix={<ThunderboltOutlined />}
                suffix="ms"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('debug.performance.score')}
                value={overallScore || 100}
                precision={0}
                valueStyle={{
                  color: overallScore >= 75 ? '#52c41a' : overallScore >= 50 ? '#faad14' : '#f5222d'
                }}
                prefix={<LineChartOutlined />}
                suffix={
                  <Tag color={
                    status === 'excellent' ? 'green' :
                    status === 'good' ? 'blue' :
                    status === 'fair' ? 'orange' :
                    status === 'poor' ? 'red' :
                    status === 'critical' ? 'purple' : 'default'
                  }>
                    {t(`debug.performance.status.${status || 'unknown'}`)}
                  </Tag>
                }
              />
            </Card>
          </Col>
        </Row>

        <Divider>{t('debug.performance.charts')}</Divider>

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Card title={t('debug.performance.fpsChart')}>
              <Line
                data={fpsHistory}
                xField="time"
                yField="value"
                point={{ size: 3 }}
                smooth
                animation={{
                  appear: {
                    animation: 'path-in',
                    duration: 1000}}}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card title={t('debug.performance.memoryChart')}>
              <Line
                data={memoryHistory}
                xField="time"
                yField="value"
                point={{ size: 3 }}
                smooth
                animation={{
                  appear: {
                    animation: 'path-in',
                    duration: 1000}}}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
          <Col span={12}>
            <Card title={t('debug.performance.cpuChart')}>
              <Line
                data={cpuHistory}
                xField="time"
                yField="value"
                point={{ size: 3 }}
                smooth
                animation={{
                  appear: {
                    animation: 'path-in',
                    duration: 1000}}}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card title={t('debug.performance.gpuChart')}>
              <Line
                data={gpuHistory}
                xField="time"
                yField="value"
                point={{ size: 3 }}
                smooth
                animation={{
                  appear: {
                    animation: 'path-in',
                    duration: 1000}}}
              />
            </Card>
          </Col>
        </Row>

        {bottlenecks && bottlenecks.length > 0 && (
          <>
            <Divider>{t('debug.performance.bottlenecks')}</Divider>
            <Card title={t('debug.performance.detectedBottlenecks')}>
              <Table
                dataSource={bottlenecks.map((bottleneck, index) => ({
                  key: index,
                  type: bottleneck.type,
                  description: bottleneck.description,
                  severity: bottleneck.severity,
                  suggestions: bottleneck.optimizationSuggestions
                }))}
                columns={[
                  {
                    title: t('debug.performance.bottleneckType'),
                    dataIndex: 'type',
                    key: 'type',
                    render: (type) => {
                      switch (type) {
                        case PerformanceBottleneckType.CPU:
                          return <Tag color="red">{t('debug.performance.bottleneck.cpu')}</Tag>;
                        case PerformanceBottleneckType.GPU:
                          return <Tag color="orange">{t('debug.performance.bottleneck.gpu')}</Tag>;
                        case PerformanceBottleneckType.MEMORY:
                          return <Tag color="purple">{t('debug.performance.bottleneck.memory')}</Tag>;
                        case PerformanceBottleneckType.NETWORK:
                          return <Tag color="blue">{t('debug.performance.bottleneck.network')}</Tag>;
                        case PerformanceBottleneckType.RENDERING:
                          return <Tag color="green">{t('debug.performance.bottleneck.rendering')}</Tag>;
                        case PerformanceBottleneckType.PHYSICS:
                          return <Tag color="cyan">{t('debug.performance.bottleneck.physics')}</Tag>;
                        case PerformanceBottleneckType.SCRIPT:
                          return <Tag color="magenta">{t('debug.performance.bottleneck.script')}</Tag>;
                        case PerformanceBottleneckType.RESOURCES:
                          return <Tag color="gold">{t('debug.performance.bottleneck.resources')}</Tag>;
                        default:
                          return <Tag>{t('debug.performance.bottleneck.unknown')}</Tag>;
                      }
                    }
                  },
                  {
                    title: t('debug.performance.description'),
                    dataIndex: 'description',
                    key: 'description'},
                  {
                    title: t('debug.performance.severity'),
                    dataIndex: 'severity',
                    key: 'severity',
                    render: (severity) => (
                      <Progress
                        percent={Math.round(severity * 100)}
                        size="small"
                        status={severity > 0.7 ? "exception" : severity > 0.4 ? "normal" : "success"}
                      />
                    )
                  }
                ]}
                size="small"
                pagination={false}
              />
            </Card>
          </>
        )}

        {optimizationSuggestions && optimizationSuggestions.length > 0 && (
          <>
            <Divider>{t('debug.performance.optimizationSuggestions')}</Divider>
            <Card title={t('debug.performance.suggestedOptimizations')}>
              <Collapse>
                {optimizationSuggestions.map((suggestion, index) => (
                  <Panel
                    header={
                      <Space>
                        <ThunderboltOutlined />
                        {suggestion.title}
                        <Tag color={
                          suggestion.priority === 'high' ? 'red' :
                          suggestion.priority === 'medium' ? 'orange' :
                          'green'
                        }>
                          {t(`debug.performance.priority.${suggestion.priority}`)}
                        </Tag>
                      </Space>
                    }
                    key={index}
                  >
                    <Paragraph>{suggestion.description}</Paragraph>
                    <Title level={5}>{t('debug.performance.implementationSteps')}</Title>
                    <ul>
                      {suggestion.implementationSteps.map((step: string, stepIndex: number) => (
                        <li key={stepIndex}>{step}</li>
                      ))}
                    </ul>
                    <Text type="secondary">{t('debug.performance.expectedImprovement')}: {suggestion.expectedImprovement}</Text>
                  </Panel>
                ))}
              </Collapse>
            </Card>
          </>
        )}
      </div>
    );
  };

  return (
    <div className={`performance-panel ${className || ''}`}>
      {renderToolbar()}

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={<span><DashboardOutlined />{t('debug.performance.overview')}</span>} key="overview">
          {renderOverviewTab()}
        </TabPane>
        <TabPane tab={<span><BarChartOutlined />{t('debug.performance.metrics')}</span>} key="metrics">
          {renderMetricsTab()}
        </TabPane>
        <TabPane tab={<span><WarningOutlined />{t('debug.performance.bottlenecks')}</span>} key="bottlenecks">
          {renderBottlenecksTab()}
        </TabPane>
        <TabPane tab={<span><LineChartOutlined />{t('debug.performance.trends')}</span>} key="trends">
          {renderTrendsTab()}
        </TabPane>
        <TabPane tab={<span><ThunderboltOutlined />{t('debug.performance.optimization')}</span>} key="optimization">
          {renderOptimizationTab()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default PerformancePanel;
